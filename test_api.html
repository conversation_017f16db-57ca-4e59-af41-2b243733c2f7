<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test Indicator API</h1>
    <button onclick="testBulkSave()">Test Bulk Save</button>
    <div id="result"></div>

    <script>
        async function testBulkSave() {
            const testData = {
                "RSI": {
                    "config": {
                        "period": 14,
                        "overbought": 70,
                        "oversold": 30
                    },
                    "is_enabled": true,
                    "display_order": 0
                }
            };

            try {
                console.log('Sending test data:', testData);
                
                const response = await fetch('/api/v1/indicators/strategies/1/indicators/bulk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                const responseText = await response.text();
                console.log('Response text:', responseText);

                document.getElementById('result').innerHTML = `
                    <h3>Response:</h3>
                    <p>Status: ${response.status}</p>
                    <p>OK: ${response.ok}</p>
                    <pre>${responseText}</pre>
                `;

            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
