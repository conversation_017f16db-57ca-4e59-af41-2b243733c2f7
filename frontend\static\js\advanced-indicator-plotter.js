/**
 * Advanced Indicator Plotter
 * Handles sophisticated chart plotting for multi-indicator system
 */

class AdvancedIndicatorPlotter {
    constructor(chartManager) {
        this.chartManager = chartManager;
        this.indicatorSeries = new Map();
        this.subcharts = new Map();
        this.currentIndicators = {};
        this.currentConfigs = {};
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        console.log('AdvancedIndicatorPlotter: Setting up event listeners...');

        // Listen for indicator configuration changes
        document.addEventListener('indicatorsConfigChanged', (event) => {
            console.log('AdvancedIndicatorPlotter: Received indicatorsConfigChanged event');
            this.handleConfigChange(event.detail);
        });

        // Listen for indicator changes from multi-indicator config manager
        document.addEventListener('indicatorsChanged', (event) => {
            console.log('AdvancedIndicatorPlotter: Received indicatorsChanged event:', event.detail);
            this.handleIndicatorChange(event.detail);
        });

        // Listen for new chart data
        document.addEventListener('chartDataUpdated', (event) => {
            console.log('AdvancedIndicatorPlotter: Received chartDataUpdated event');
            this.handleDataUpdate(event.detail);
        });

        console.log('AdvancedIndicatorPlotter: Event listeners set up successfully');
    }

    async handleConfigChange(detail) {
        const { strategyId, configs } = detail;
        this.currentConfigs = configs;

        // Recalculate and plot indicators with new configuration
        await this.recalculateAndPlotIndicators(strategyId);
    }

    async handleIndicatorChange(detail) {
        console.log('AdvancedIndicatorPlotter: handleIndicatorChange called with detail:', detail);
        const { strategyId, indicators } = detail;

        // Convert indicators format to match config format
        const configs = {};
        for (const [name, config] of Object.entries(indicators)) {
            configs[name] = {
                indicator_name: name,
                config: config,
                is_enabled: true
            };
        }

        console.log('AdvancedIndicatorPlotter: Converted configs:', configs);
        this.currentConfigs = configs;

        // Recalculate and plot indicators
        console.log('AdvancedIndicatorPlotter: Calling recalculateAndPlotIndicators...');
        await this.recalculateAndPlotIndicators(strategyId);
    }

    async handleDataUpdate(detail) {
        const { ohlcvData, strategyId } = detail;
        
        if (Object.keys(this.currentConfigs).length > 0) {
            await this.calculateAndPlotIndicators(ohlcvData, strategyId);
        }
    }

    async recalculateAndPlotIndicators(strategyId) {
        // Get current chart data
        const chartData = this.getCurrentChartData();
        if (!chartData || chartData.length === 0) {
            console.warn('No chart data available for indicator calculation');
            return;
        }

        await this.calculateAndPlotIndicators(chartData, strategyId);
    }

    async calculateAndPlotIndicators(ohlcvData, strategyId) {
        try {
            // Prepare enabled indicators configuration
            const enabledConfigs = {};
            for (const [name, config] of Object.entries(this.currentConfigs)) {
                if (config.is_enabled) {
                    enabledConfigs[name] = config.config;
                }
            }

            if (Object.keys(enabledConfigs).length === 0) {
                this.clearAllIndicators();
                return;
            }

            // Calculate indicators via API
            const response = await fetch('/api/v1/indicators/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    symbol: this.getCurrentSymbol(),
                    timeframe: this.getCurrentTimeframe(),
                    ohlcv_data: ohlcvData,
                    indicators_config: enabledConfigs,
                    strategy_id: strategyId
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.currentIndicators = data.data;
                this.plotAllIndicators();
            } else {
                console.error('Failed to calculate indicators:', data.message);
            }
        } catch (error) {
            console.error('Error calculating indicators:', error);
        }
    }

    plotAllIndicators() {
        // Clear existing indicators
        this.clearAllIndicators();

        // Plot each enabled indicator
        for (const [indicatorName, config] of Object.entries(this.currentConfigs)) {
            if (!config.is_enabled) continue;

            const indicatorData = this.currentIndicators[indicatorName.toLowerCase()];
            if (!indicatorData) continue;

            this.plotIndicator(indicatorName, indicatorData, config);
        }
    }

    plotIndicator(indicatorName, indicatorData, config) {
        const indicatorConfig = config.config;
        const chartType = this.getIndicatorChartType(indicatorName);

        switch (indicatorName) {
            case 'EMA':
            case 'SMA':
                this.plotMultiLineIndicator(indicatorName, indicatorData, indicatorConfig, 'overlay');
                break;
            case 'RSI':
                this.plotRSI(indicatorData, indicatorConfig);
                break;
            case 'MACD':
                this.plotMACD(indicatorData, indicatorConfig);
                break;
            case 'BOLLINGER_BANDS':
                this.plotBollingerBands(indicatorData, indicatorConfig);
                break;
            case 'STOCHASTIC':
                this.plotStochastic(indicatorData, indicatorConfig);
                break;
            default:
                this.plotGenericIndicator(indicatorName, indicatorData, indicatorConfig, chartType);
                break;
        }
    }

    plotMultiLineIndicator(indicatorName, indicatorData, config, chartType) {
        const periods = config.periods || [];
        const colors = config.colors || [];
        const lineWidth = config.lineWidth || 2;

        periods.forEach((period, index) => {
            const seriesKey = `${indicatorName}_${period}`;
            const values = indicatorData[seriesKey];
            
            if (!values) return;

            const seriesData = this.prepareSeriesData(values);
            const color = colors[index] || '#2196F3';

            if (chartType === 'overlay') {
                this.addOverlaySeries(seriesKey, seriesData, {
                    color: color,
                    lineWidth: lineWidth,
                    title: `${indicatorName}(${period})`
                });
            } else {
                this.addSubchartSeries(indicatorName, seriesKey, seriesData, {
                    color: color,
                    lineWidth: lineWidth,
                    title: `${indicatorName}(${period})`
                });
            }
        });
    }

    plotRSI(indicatorData, config) {
        const periods = config.periods || [14];
        const colors = config.colors || ['#2196F3'];
        const overbought = config.overbought || 70;
        const oversold = config.oversold || 30;
        const lineWidth = config.lineWidth || 2;

        // Create RSI subchart if it doesn't exist
        if (!this.subcharts.has('RSI')) {
            this.createSubchart('RSI', {
                height: 150,
                priceScale: {
                    mode: 0, // Normal mode
                    autoScale: false,
                    scaleMargins: { top: 0.1, bottom: 0.1 },
                    borderVisible: false,
                },
                timeScale: { visible: false }
            });
        }

        const subchart = this.subcharts.get('RSI');

        periods.forEach((period, index) => {
            const seriesKey = `RSI_${period}`;
            const values = indicatorData[seriesKey];
            
            if (!values) return;

            const seriesData = this.prepareSeriesData(values);
            const color = colors[index] || '#2196F3';

            const series = subchart.addLineSeries({
                color: color,
                lineWidth: lineWidth,
                title: `RSI(${period})`
            });

            series.setData(seriesData);
            this.indicatorSeries.set(seriesKey, series);
        });

        // Add overbought/oversold lines
        this.addHorizontalLine('RSI', overbought, '#ff4444', 'Overbought');
        this.addHorizontalLine('RSI', oversold, '#44ff44', 'Oversold');
    }

    plotMACD(indicatorData, config) {
        const colors = config.colors || {
            macd: '#2196F3',
            signal: '#FF9800',
            histogram: '#4CAF50'
        };
        const lineWidth = config.lineWidth || 2;

        // Create MACD subchart if it doesn't exist
        if (!this.subcharts.has('MACD')) {
            this.createSubchart('MACD', {
                height: 150,
                priceScale: {
                    mode: 0,
                    autoScale: true,
                    scaleMargins: { top: 0.1, bottom: 0.1 },
                    borderVisible: false,
                },
                timeScale: { visible: false }
            });
        }

        const subchart = this.subcharts.get('MACD');

        // Plot MACD line
        if (indicatorData.macd) {
            const macdSeries = subchart.addLineSeries({
                color: colors.macd,
                lineWidth: lineWidth,
                title: 'MACD'
            });
            macdSeries.setData(this.prepareSeriesData(indicatorData.macd));
            this.indicatorSeries.set('MACD_macd', macdSeries);
        }

        // Plot Signal line
        if (indicatorData.signal) {
            const signalSeries = subchart.addLineSeries({
                color: colors.signal,
                lineWidth: lineWidth,
                title: 'Signal'
            });
            signalSeries.setData(this.prepareSeriesData(indicatorData.signal));
            this.indicatorSeries.set('MACD_signal', signalSeries);
        }

        // Plot Histogram
        if (indicatorData.histogram) {
            const histogramSeries = subchart.addHistogramSeries({
                color: colors.histogram,
                title: 'Histogram'
            });
            histogramSeries.setData(this.prepareHistogramData(indicatorData.histogram));
            this.indicatorSeries.set('MACD_histogram', histogramSeries);
        }
    }

    plotBollingerBands(indicatorData, config) {
        const colors = config.colors || {
            upper: '#FF5722',
            middle: '#607D8B',
            lower: '#FF5722'
        };
        const fillOpacity = config.fillOpacity || 0.1;
        const lineWidth = config.lineWidth || 1;

        // Plot upper band
        if (indicatorData.upper) {
            this.addOverlaySeries('BB_upper', this.prepareSeriesData(indicatorData.upper), {
                color: colors.upper,
                lineWidth: lineWidth,
                title: 'BB Upper'
            });
        }

        // Plot middle band (SMA)
        if (indicatorData.middle) {
            this.addOverlaySeries('BB_middle', this.prepareSeriesData(indicatorData.middle), {
                color: colors.middle,
                lineWidth: lineWidth,
                title: 'BB Middle'
            });
        }

        // Plot lower band
        if (indicatorData.lower) {
            this.addOverlaySeries('BB_lower', this.prepareSeriesData(indicatorData.lower), {
                color: colors.lower,
                lineWidth: lineWidth,
                title: 'BB Lower'
            });
        }

        // Add fill between bands (if supported by chart library)
        this.addBandFill('BB', indicatorData.upper, indicatorData.lower, colors.upper, fillOpacity);
    }

    plotStochastic(indicatorData, config) {
        const colors = config.colors || {
            k: '#E91E63',
            d: '#9C27B0'
        };
        const overbought = config.overbought || 80;
        const oversold = config.oversold || 20;
        const lineWidth = config.lineWidth || 2;

        // Create Stochastic subchart if it doesn't exist
        if (!this.subcharts.has('STOCHASTIC')) {
            this.createSubchart('STOCHASTIC', {
                height: 150,
                priceScale: {
                    mode: 0,
                    autoScale: false,
                    scaleMargins: { top: 0.1, bottom: 0.1 },
                    borderVisible: false,
                },
                timeScale: { visible: false }
            });
        }

        const subchart = this.subcharts.get('STOCHASTIC');

        // Plot %K line
        if (indicatorData.k) {
            const kSeries = subchart.addLineSeries({
                color: colors.k,
                lineWidth: lineWidth,
                title: '%K'
            });
            kSeries.setData(this.prepareSeriesData(indicatorData.k));
            this.indicatorSeries.set('STOCH_k', kSeries);
        }

        // Plot %D line
        if (indicatorData.d) {
            const dSeries = subchart.addLineSeries({
                color: colors.d,
                lineWidth: lineWidth,
                title: '%D'
            });
            dSeries.setData(this.prepareSeriesData(indicatorData.d));
            this.indicatorSeries.set('STOCH_d', dSeries);
        }

        // Add overbought/oversold lines
        this.addHorizontalLine('STOCHASTIC', overbought, '#ff4444', 'Overbought');
        this.addHorizontalLine('STOCHASTIC', oversold, '#44ff44', 'Oversold');
    }

    plotGenericIndicator(indicatorName, indicatorData, config, chartType) {
        const color = config.color || '#2196F3';
        const lineWidth = config.lineWidth || 2;

        // Assume single value indicator
        const values = indicatorData[Object.keys(indicatorData)[0]];
        if (!values) return;

        const seriesData = this.prepareSeriesData(values);

        if (chartType === 'overlay') {
            this.addOverlaySeries(indicatorName, seriesData, {
                color: color,
                lineWidth: lineWidth,
                title: indicatorName
            });
        } else {
            this.addSubchartSeries(indicatorName, indicatorName, seriesData, {
                color: color,
                lineWidth: lineWidth,
                title: indicatorName
            });
        }
    }

    // Utility methods
    prepareSeriesData(values) {
        const timestamps = this.currentIndicators.timestamps || [];
        return values.map((value, index) => ({
            time: timestamps[index] ? new Date(timestamps[index]).getTime() / 1000 : index,
            value: value || 0
        })).filter(item => item.value !== null && !isNaN(item.value));
    }

    prepareHistogramData(values) {
        const timestamps = this.currentIndicators.timestamps || [];
        return values.map((value, index) => ({
            time: timestamps[index] ? new Date(timestamps[index]).getTime() / 1000 : index,
            value: value || 0,
            color: value >= 0 ? '#4CAF50' : '#f44336'
        })).filter(item => item.value !== null && !isNaN(item.value));
    }

    addOverlaySeries(seriesKey, data, options) {
        if (!this.chartManager || !this.chartManager.chart) return;

        const series = this.chartManager.chart.addLineSeries({
            color: options.color,
            lineWidth: options.lineWidth,
            title: options.title,
            priceLineVisible: false,
            lastValueVisible: true
        });

        series.setData(data);
        this.indicatorSeries.set(seriesKey, series);
    }

    addSubchartSeries(subchartName, seriesKey, data, options) {
        if (!this.subcharts.has(subchartName)) {
            this.createSubchart(subchartName);
        }

        const subchart = this.subcharts.get(subchartName);
        const series = subchart.addLineSeries({
            color: options.color,
            lineWidth: options.lineWidth,
            title: options.title,
            priceLineVisible: false,
            lastValueVisible: true
        });

        series.setData(data);
        this.indicatorSeries.set(seriesKey, series);
    }

    createSubchart(name, options = {}) {
        // Create a new chart container for subchart
        const container = document.createElement('div');
        container.id = `subchart-${name}`;
        container.style.height = `${options.height || 150}px`;
        container.style.marginTop = '10px';
        container.style.border = '1px solid #333';
        container.style.borderRadius = '4px';

        // Add to chart container
        const chartContainer = document.getElementById('professional-tradingview-chart');
        if (chartContainer) {
            chartContainer.parentNode.appendChild(container);
        }

        // Create subchart
        const subchart = LightweightCharts.createChart(container, {
            width: container.clientWidth,
            height: options.height || 150,
            layout: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
            },
            grid: {
                vertLines: { color: '#363c4e' },
                horzLines: { color: '#363c4e' },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            timeScale: {
                visible: options.timeScale?.visible !== false,
                borderColor: '#485c7b',
            },
            priceScale: {
                borderColor: '#485c7b',
                ...options.priceScale
            },
            handleScroll: {
                mouseWheel: true,
                pressedMouseMove: true,
            },
            handleScale: {
                axisPressedMouseMove: true,
                mouseWheel: true,
                pinch: true,
            },
        });

        this.subcharts.set(name, subchart);

        // Sync time scale with main chart
        this.syncTimeScales(subchart);

        return subchart;
    }

    syncTimeScales(subchart) {
        if (!this.chartManager || !this.chartManager.chart) return;

        const mainChart = this.chartManager.chart;
        const mainTimeScale = mainChart.timeScale();
        const subTimeScale = subchart.timeScale();

        // Sync visible range
        mainTimeScale.subscribeVisibleTimeRangeChange((timeRange) => {
            if (timeRange) {
                subTimeScale.setVisibleRange(timeRange);
            }
        });

        subTimeScale.subscribeVisibleTimeRangeChange((timeRange) => {
            if (timeRange) {
                mainTimeScale.setVisibleRange(timeRange);
            }
        });
    }

    addHorizontalLine(subchartName, price, color, title) {
        const subchart = this.subcharts.get(subchartName);
        if (!subchart) return;

        const series = subchart.addLineSeries({
            color: color,
            lineWidth: 1,
            lineStyle: LightweightCharts.LineStyle.Dashed,
            title: title,
            priceLineVisible: false,
            lastValueVisible: false
        });

        // Create horizontal line data
        const timestamps = this.currentIndicators.timestamps || [];
        const lineData = timestamps.map(timestamp => ({
            time: new Date(timestamp).getTime() / 1000,
            value: price
        }));

        series.setData(lineData);
        this.indicatorSeries.set(`${subchartName}_${title}`, series);
    }

    clearAllIndicators() {
        // Remove all indicator series
        this.indicatorSeries.forEach((series, key) => {
            try {
                if (series && typeof series.remove === 'function') {
                    series.remove();
                }
            } catch (error) {
                console.warn(`Error removing series ${key}:`, error);
            }
        });
        this.indicatorSeries.clear();

        // Remove all subcharts
        this.subcharts.forEach((subchart, name) => {
            try {
                const container = document.getElementById(`subchart-${name}`);
                if (container) {
                    container.remove();
                }
                if (subchart && typeof subchart.remove === 'function') {
                    subchart.remove();
                }
            } catch (error) {
                console.warn(`Error removing subchart ${name}:`, error);
            }
        });
        this.subcharts.clear();
    }

    getCurrentChartData() {
        // Get current OHLCV data from chart manager
        if (this.chartManager && this.chartManager.currentData) {
            return this.chartManager.currentData;
        }

        // Fallback: try to get from global chart instance
        if (window.professionalChart && window.professionalChart.currentData) {
            return window.professionalChart.currentData;
        }

        return [];
    }

    getCurrentSymbol() {
        return this.chartManager?.currentSymbol ||
               window.professionalChart?.config?.symbol ||
               'BTCUSDT';
    }

    getCurrentTimeframe() {
        return this.chartManager?.currentTimeframe ||
               window.professionalChart?.config?.interval ||
               '15m';
    }
}

// Global instance
let advancedIndicatorPlotter = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('AdvancedIndicatorPlotter: DOM loaded, starting initialization...');

    // Wait for chart manager to be available
    const initPlotter = () => {
        console.log('AdvancedIndicatorPlotter: Checking for professionalChart...', !!window.professionalChart);

        if (window.professionalChart) {
            console.log('AdvancedIndicatorPlotter: professionalChart found, creating plotter...');
            advancedIndicatorPlotter = new AdvancedIndicatorPlotter(window.professionalChart);
            window.advancedIndicatorPlotter = advancedIndicatorPlotter;
            console.log('AdvancedIndicatorPlotter: Advanced Indicator Plotter initialized successfully');
        } else {
            console.log('AdvancedIndicatorPlotter: professionalChart not ready, retrying in 100ms...');
            setTimeout(initPlotter, 100);
        }
    };

    // Add a longer delay to ensure all components are loaded
    setTimeout(initPlotter, 500);
});
