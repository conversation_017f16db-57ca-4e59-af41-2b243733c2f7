#!/usr/bin/env python3
"""
Manual migration runner
"""
import sys
import os
sys.path.append('.')

from backend.app.core.database import get_db_cursor

def run_migration():
    """Run the migration manually"""
    
    # SQL statements to create the required tables
    sql_statements = [
        """
        CREATE TABLE IF NOT EXISTS indicator_defaults (
            id INT AUTO_INCREMENT PRIMARY KEY,
            indicator_name VARCHAR(50) NOT NULL UNIQUE,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            default_config JSON NOT NULL,
            chart_type ENUM('overlay', 'subchart') NOT NULL DEFAULT 'overlay',
            supports_multiple BOOLEAN DEFAULT FALSE,
            parameter_schema JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_chart_type (chart_type),
            INDEX idx_supports_multiple (supports_multiple)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS strategy_indicator_configs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            strategy_id INT NOT NULL,
            indicator_name VARCHAR(50) NOT NULL,
            config JSON NOT NULL,
            is_enabled BOOLEAN DEFAULT TRUE,
            display_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_strategy_indicator (strategy_id, indicator_name),
            INDEX idx_strategy_enabled (strategy_id, is_enabled),
            INDEX idx_display_order (display_order),
            FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        INSERT IGNORE INTO indicator_defaults (indicator_name, display_name, description, default_config, chart_type, supports_multiple) VALUES
        ('RSI', 'Relative Strength Index', 'RSI oscillator indicator', '{"period": 14, "overbought": 70, "oversold": 30, "color": "#FF6B6B"}', 'subchart', false),
        ('MACD', 'MACD', 'Moving Average Convergence Divergence', '{"fast": 12, "slow": 26, "signal": 9, "colors": {"macd": "#2196F3", "signal": "#FF9800", "histogram": "#4CAF50"}}', 'subchart', false),
        ('EMA', 'Exponential Moving Average', 'Exponential Moving Average', '{"periods": [20, 50], "colors": ["#FF6B6B", "#4ECDC4"], "lineWidth": 2}', 'overlay', true),
        ('SMA', 'Simple Moving Average', 'Simple Moving Average', '{"periods": [20, 50], "colors": ["#9C27B0", "#FF5722"], "lineWidth": 2}', 'overlay', true)
        """
    ]
    
    try:
        with get_db_cursor() as cursor:
            print("Running migration...")
            
            for i, sql in enumerate(sql_statements, 1):
                try:
                    print(f"Executing statement {i}...")
                    cursor.execute(sql)
                    print(f"✅ Statement {i} executed successfully")
                except Exception as e:
                    print(f"⚠️ Statement {i} failed: {e}")
            
            print("✅ Migration completed!")
            
            # Verify tables exist
            cursor.execute("SHOW TABLES LIKE 'indicator_defaults'")
            if cursor.fetchone():
                print("✅ indicator_defaults table exists")
            else:
                print("❌ indicator_defaults table missing")
                
            cursor.execute("SHOW TABLES LIKE 'strategy_indicator_configs'")
            if cursor.fetchone():
                print("✅ strategy_indicator_configs table exists")
            else:
                print("❌ strategy_indicator_configs table missing")
                
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if run_migration():
        print("🎉 Migration completed successfully!")
    else:
        print("💥 Migration failed!")
        sys.exit(1)
