#!/usr/bin/env python3
"""
Test script to debug the bulk save API endpoint
"""
import requests
import json

def test_bulk_save():
    """Test the bulk save endpoint"""
    
    # Test data
    test_data = {
        "RSI": {
            "config": {
                "period": 14,
                "overbought": 70,
                "oversold": 30
            },
            "is_enabled": True,
            "display_order": 0
        }
    }
    
    url = "http://127.0.0.1:8000/api/v1/indicators/strategies/1/indicators/bulk"
    
    print(f"Testing URL: {url}")
    print(f"Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"Response JSON: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_bulk_save()
