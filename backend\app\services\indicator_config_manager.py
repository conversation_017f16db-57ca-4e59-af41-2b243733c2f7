"""
Indicator Configuration Manager
Handles indicator configurations, defaults, and strategy associations
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..core.database import get_db_cursor
from ..core.exceptions import DatabaseError, ValidationError
from .multi_indicator_engine import MultiIndicatorEngine

logger = logging.getLogger(__name__)

class IndicatorConfigManager:
    """
    Manages indicator configurations, defaults, and strategy associations
    """

    @staticmethod
    def _ensure_tables_exist(cursor):
        """
        Ensure required tables exist, create them if they don't

        Args:
            cursor: Database cursor
        """
        try:
            # Create indicator_defaults table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS indicator_defaults (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    indicator_name VARCHAR(50) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    default_config JSON NOT NULL,
                    chart_type ENUM('overlay', 'subchart') NOT NULL DEFAULT 'overlay',
                    supports_multiple BOOLEAN DEFAULT FALSE,
                    parameter_schema JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_chart_type (chart_type),
                    INDEX idx_supports_multiple (supports_multiple)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)

            # Create strategy_indicator_configs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_indicator_configs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    strategy_id INT NOT NULL,
                    indicator_name VARCHAR(50) NOT NULL,
                    config JSON NOT NULL,
                    is_enabled BOOLEAN DEFAULT TRUE,
                    display_order INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_strategy_indicator (strategy_id, indicator_name),
                    INDEX idx_strategy_enabled (strategy_id, is_enabled),
                    INDEX idx_display_order (display_order)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)

            # Insert default indicator configurations if they don't exist
            cursor.execute("""
                INSERT IGNORE INTO indicator_defaults (indicator_name, display_name, description, default_config, chart_type, supports_multiple) VALUES
                ('RSI', 'Relative Strength Index', 'RSI oscillator indicator', '{"period": 14, "overbought": 70, "oversold": 30, "color": "#FF6B6B"}', 'subchart', false),
                ('MACD', 'MACD', 'Moving Average Convergence Divergence', '{"fast": 12, "slow": 26, "signal": 9, "colors": {"macd": "#2196F3", "signal": "#FF9800", "histogram": "#4CAF50"}}', 'subchart', false),
                ('EMA', 'Exponential Moving Average', 'Exponential Moving Average', '{"periods": [20, 50], "colors": ["#FF6B6B", "#4ECDC4"], "lineWidth": 2}', 'overlay', true),
                ('SMA', 'Simple Moving Average', 'Simple Moving Average', '{"periods": [20, 50], "colors": ["#9C27B0", "#FF5722"], "lineWidth": 2}', 'overlay', true),
                ('BOLLINGER_BANDS', 'Bollinger Bands', 'Bollinger Bands indicator', '{"period": 20, "stdDev": 2, "colors": {"upper": "#FF6B6B", "middle": "#4ECDC4", "lower": "#45B7D1"}}', 'overlay', false),
                ('STOCHASTIC', 'Stochastic Oscillator', 'Stochastic oscillator indicator', '{"kPeriod": 14, "dPeriod": 3, "colors": {"k": "#FF6B6B", "d": "#4ECDC4"}}', 'subchart', false),
                ('ATR', 'Average True Range', 'Average True Range indicator', '{"period": 14, "color": "#FF6B6B"}', 'subchart', false),
                ('VOLUME_SMA', 'Volume SMA', 'Volume Simple Moving Average', '{"period": 20, "color": "#4ECDC4"}', 'subchart', false)
            """)

            logger.info("Database tables ensured and default data inserted")

        except Exception as e:
            logger.warning(f"Error ensuring tables exist: {e}")
            # Don't raise exception here, let the calling method handle it
    
    @staticmethod
    def get_indicator_defaults(indicator_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get default configurations for indicators
        
        Args:
            indicator_name: Specific indicator name, or None for all
            
        Returns:
            Dictionary of default configurations
        """
        try:
            with get_db_cursor() as cursor:
                # Ensure tables exist
                IndicatorConfigManager._ensure_tables_exist(cursor)

                if indicator_name:
                    cursor.execute("""
                        SELECT indicator_name, display_name, description, default_config, 
                               chart_type, supports_multiple, parameter_schema
                        FROM indicator_defaults 
                        WHERE indicator_name = %s
                    """, (indicator_name.upper(),))
                    result = cursor.fetchone()
                    
                    if result:
                        return {
                            'indicator_name': result[0],
                            'display_name': result[1],
                            'description': result[2],
                            'default_config': json.loads(result[3]) if result[3] else {},
                            'chart_type': result[4],
                            'supports_multiple': result[5],
                            'parameter_schema': json.loads(result[6]) if result[6] else {}
                        }
                    else:
                        # Fallback to engine defaults
                        return {
                            'indicator_name': indicator_name.upper(),
                            'default_config': MultiIndicatorEngine.get_default_config(indicator_name),
                            'chart_type': 'overlay',
                            'supports_multiple': False
                        }
                else:
                    cursor.execute("""
                        SELECT indicator_name, display_name, description, default_config, 
                               chart_type, supports_multiple, parameter_schema
                        FROM indicator_defaults 
                        ORDER BY indicator_name
                    """)
                    results = cursor.fetchall()
                    
                    defaults = {}
                    for row in results:
                        defaults[row[0]] = {
                            'indicator_name': row[0],
                            'display_name': row[1],
                            'description': row[2],
                            'default_config': json.loads(row[3]) if row[3] else {},
                            'chart_type': row[4],
                            'supports_multiple': row[5],
                            'parameter_schema': json.loads(row[6]) if row[6] else {}
                        }
                    
                    return defaults
                    
        except Exception as e:
            logger.error(f"Error getting indicator defaults: {e}")
            # Fallback to engine defaults
            if indicator_name:
                return {
                    'indicator_name': indicator_name.upper(),
                    'default_config': MultiIndicatorEngine.get_default_config(indicator_name),
                    'chart_type': 'overlay',
                    'supports_multiple': False
                }
            else:
                defaults = {}
                for name in MultiIndicatorEngine.get_supported_indicators():
                    defaults[name] = {
                        'indicator_name': name,
                        'default_config': MultiIndicatorEngine.get_default_config(name),
                        'chart_type': 'overlay',
                        'supports_multiple': False
                    }
                return defaults

    @staticmethod
    def save_strategy_indicator_config(strategy_id: int, indicator_name: str,
                                     config: Dict[str, Any], is_enabled: bool = True,
                                     display_order: int = 0) -> bool:
        """
        Save indicator configuration for a strategy

        Args:
            strategy_id: Strategy ID
            indicator_name: Indicator name
            config: Configuration dictionary
            is_enabled: Whether indicator is enabled
            display_order: Display order for UI

        Returns:
            True if successful
        """
        try:
            # Validate configuration
            if not MultiIndicatorEngine.validate_config(indicator_name, config):
                raise ValidationError(f"Invalid configuration for {indicator_name}")

            with get_db_cursor() as cursor:
                # First, ensure the table exists
                IndicatorConfigManager._ensure_tables_exist(cursor)

                cursor.execute("""
                    INSERT INTO strategy_indicator_configs
                    (strategy_id, indicator_name, config, is_enabled, display_order)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    config = VALUES(config),
                    is_enabled = VALUES(is_enabled),
                    display_order = VALUES(display_order),
                    updated_at = CURRENT_TIMESTAMP
                """, (strategy_id, indicator_name.upper(), json.dumps(config), is_enabled, display_order))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error saving strategy indicator config: {e}")
            raise DatabaseError(f"Failed to save indicator config: {e}")

    @staticmethod
    def get_strategy_indicator_configs(strategy_id: int) -> Dict[str, Any]:
        """
        Get all indicator configurations for a strategy

        Args:
            strategy_id: Strategy ID

        Returns:
            Dictionary of indicator configurations
        """
        try:
            with get_db_cursor() as cursor:
                # Ensure tables exist
                IndicatorConfigManager._ensure_tables_exist(cursor)

                cursor.execute("""
                    SELECT sic.indicator_name, sic.config, sic.is_enabled, sic.display_order,
                           idef.display_name, idef.chart_type, idef.supports_multiple
                    FROM strategy_indicator_configs sic
                    LEFT JOIN indicator_defaults idef ON sic.indicator_name = idef.indicator_name
                    WHERE sic.strategy_id = %s
                    ORDER BY sic.display_order, sic.indicator_name
                """, (strategy_id,))

                results = cursor.fetchall()
                configs = {}

                for row in results:
                    indicator_name = row[0]
                    configs[indicator_name] = {
                        'indicator_name': indicator_name,
                        'config': json.loads(row[1]) if row[1] else {},
                        'is_enabled': row[2],
                        'display_order': row[3],
                        'display_name': row[4] or indicator_name,
                        'chart_type': row[5] or 'overlay',
                        'supports_multiple': row[6] or False
                    }

                return configs

        except Exception as e:
            logger.error(f"Error getting strategy indicator configs: {e}")
            return {}

    @staticmethod
    def update_strategy_indicator_config(strategy_id: int, indicator_name: str, 
                                       updates: Dict[str, Any]) -> bool:
        """
        Update specific fields of an indicator configuration
        
        Args:
            strategy_id: Strategy ID
            indicator_name: Indicator name
            updates: Dictionary of fields to update
            
        Returns:
            True if successful
        """
        try:
            # Build dynamic update query
            update_fields = []
            values = []
            
            if 'config' in updates:
                if not MultiIndicatorEngine.validate_config(indicator_name, updates['config']):
                    raise ValidationError(f"Invalid configuration for {indicator_name}")
                update_fields.append("config = %s")
                values.append(json.dumps(updates['config']))
            
            if 'is_enabled' in updates:
                update_fields.append("is_enabled = %s")
                values.append(updates['is_enabled'])
            
            if 'display_order' in updates:
                update_fields.append("display_order = %s")
                values.append(updates['display_order'])
            
            if not update_fields:
                return True  # Nothing to update
            
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            values.extend([strategy_id, indicator_name.upper()])
            
            with get_db_cursor() as cursor:
                sql = f"""
                    UPDATE strategy_indicator_configs 
                    SET {', '.join(update_fields)}
                    WHERE strategy_id = %s AND indicator_name = %s
                """
                cursor.execute(sql, values)
                
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error updating strategy indicator config: {e}")
            raise DatabaseError(f"Failed to update indicator config: {e}")

    @staticmethod
    def delete_strategy_indicator_config(strategy_id: int, indicator_name: str) -> bool:
        """
        Delete indicator configuration from strategy
        
        Args:
            strategy_id: Strategy ID
            indicator_name: Indicator name
            
        Returns:
            True if successful
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    DELETE FROM strategy_indicator_configs 
                    WHERE strategy_id = %s AND indicator_name = %s
                """, (strategy_id, indicator_name.upper()))
                
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error deleting strategy indicator config: {e}")
            raise DatabaseError(f"Failed to delete indicator config: {e}")

    @staticmethod
    def get_enabled_indicators_for_strategy(strategy_id: int) -> Dict[str, Dict[str, Any]]:
        """
        Get only enabled indicator configurations for a strategy
        
        Args:
            strategy_id: Strategy ID
            
        Returns:
            Dictionary of enabled indicator configurations
        """
        all_configs = IndicatorConfigManager.get_strategy_indicator_configs(strategy_id)
        return {name: config for name, config in all_configs.items() if config.get('is_enabled', True)}

    @staticmethod
    def bulk_save_strategy_indicators(strategy_id: int, indicators_config: Dict[str, Dict[str, Any]]) -> bool:
        """
        Save multiple indicator configurations for a strategy in bulk
        
        Args:
            strategy_id: Strategy ID
            indicators_config: Dictionary of indicator configurations
            
        Returns:
            True if all successful
        """
        try:
            success_count = 0
            
            for indicator_name, config_data in indicators_config.items():
                config = config_data.get('config', {})
                is_enabled = config_data.get('is_enabled', True)
                display_order = config_data.get('display_order', 0)
                
                if IndicatorConfigManager.save_strategy_indicator_config(
                    strategy_id, indicator_name, config, is_enabled, display_order
                ):
                    success_count += 1
            
            return success_count == len(indicators_config)
            
        except Exception as e:
            logger.error(f"Error in bulk save strategy indicators: {e}")
            return False

    @staticmethod
    def merge_with_defaults(strategy_id: int, indicator_name: str) -> Dict[str, Any]:
        """
        Get indicator configuration merged with defaults
        
        Args:
            strategy_id: Strategy ID
            indicator_name: Indicator name
            
        Returns:
            Merged configuration with defaults
        """
        try:
            # Get strategy-specific config
            strategy_configs = IndicatorConfigManager.get_strategy_indicator_configs(strategy_id)
            strategy_config = strategy_configs.get(indicator_name.upper(), {})
            
            # Get defaults
            defaults = IndicatorConfigManager.get_indicator_defaults(indicator_name)
            
            # Merge configurations
            merged_config = defaults.get('default_config', {}).copy()
            merged_config.update(strategy_config.get('config', {}))
            
            return {
                'indicator_name': indicator_name.upper(),
                'config': merged_config,
                'is_enabled': strategy_config.get('is_enabled', True),
                'display_name': defaults.get('display_name', indicator_name),
                'chart_type': defaults.get('chart_type', 'overlay'),
                'supports_multiple': defaults.get('supports_multiple', False)
            }
            
        except Exception as e:
            logger.error(f"Error merging with defaults: {e}")
            return {
                'indicator_name': indicator_name.upper(),
                'config': MultiIndicatorEngine.get_default_config(indicator_name),
                'is_enabled': True,
                'chart_type': 'overlay',
                'supports_multiple': False
            }
